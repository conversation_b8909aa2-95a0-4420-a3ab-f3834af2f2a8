import React, { useState, useContext, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    KeyboardAvoidingView,
    Platform,
    Image,
    Dimensions,
    ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';
import { showToast } from '../../utils/showToast';
import { styles } from './siteStyles';
import { useSiteDetails } from '../../api/sites/getSiteById';

// Import step components (reuse from AddSites)
import SiteDetailsStep from './AddSites/SiteDetailsStep';
import LocationStep from './AddSites/LocationStep';
import EncumbranceStep from './AddSites/EncumbranceStep';
import PropertyTaxStep from './AddSites/PropertyTaxStep';
import ReviewStep from './AddSites/ReviewStep';

const { height } = Dimensions.get('window');

const UpdateSite = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const queryClient = useQueryClient();
    const { siteId } = useLocalSearchParams();
    const [step, setStep] = useState('siteDetails');
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Fetch existing site data
    const {
        fields: existingFields,
        isLoading,
        setFields,
    } = useSiteDetails(siteId);

    // Map state
    const [region, setRegion] = useState({
        latitude: 37.78825,
        longitude: -122.4324,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
    });

    const [marker, setMarker] = useState(null);
    const [hasPermission, setHasPermission] = useState(false);

    // Update map region and marker when fields change
    useEffect(() => {
        if (existingFields.latitude && existingFields.longitude) {
            const lat = parseFloat(existingFields.latitude);
            const lon = parseFloat(existingFields.longitude);
            if (!isNaN(lat) && !isNaN(lon)) {
                setRegion({
                    latitude: lat,
                    longitude: lon,
                    latitudeDelta: 0.0922,
                    longitudeDelta: 0.0421,
                });
                setMarker({
                    latitude: lat,
                    longitude: lon,
                });
            }
        }
    }, [existingFields.latitude, existingFields.longitude]);

    // Site update mutation
    const { mutateAsync: updateSite, isLoading: isUpdating } = useMutation({
        mutationFn: async (formData) => {
            const { privateAPIClient } = await import('../../api');
            return privateAPIClient.put(
                `/site-service/api/v1/sites/${siteId}`,
                formData,
                {
                    headers: { 'Content-Type': 'multipart/form-data' },
                }
            );
        },
        onSuccess: () => {
            setIsSubmitting(false);
            queryClient.invalidateQueries({ queryKey: ['sites'] });
            queryClient.invalidateQueries({ queryKey: ['site', siteId] });
            router.replace('/Sites/SiteSuccess');
        },
        onError: (error) => {
            setIsSubmitting(false);
            showToast(
                'error',
                'Update Error',
                'Failed to update site listing.'
            );
        },
    });

    const handleSubmit = async () => {
        if (
            !existingFields.siteImages?.length ||
            !existingFields.encumbranceCert ||
            !existingFields.propertyTaxRec
        ) {
            showToast(
                'error',
                'Missing Documents',
                'Please attach at least one site image and the required documents.'
            );
            return;
        }

        if (
            !existingFields.name ||
            !existingFields.pincode ||
            !existingFields.plotArea
        ) {
            showToast(
                'error',
                'Missing Fields',
                'Name, pincode, and plot area are required.'
            );
            return;
        }

        setIsSubmitting(true);

        const buildPart = (asset) => ({
            uri: asset.uri,
            name: asset.name || `file_${Date.now()}`,
            type: asset.mimeType || 'application/octet-stream',
        });

        const form = new FormData();

        // Handle site images
        if (existingFields.siteImages?.length) {
            existingFields.siteImages.forEach((image) => {
                if (image.uri) {
                    form.append('siteImages', buildPart(image));
                }
            });
        }

        // Handle documents
        if (existingFields.encumbranceCert?.uri) {
            form.append(
                'encumbranceCertificate',
                buildPart(existingFields.encumbranceCert)
            );
        }
        if (existingFields.propertyTaxRec?.uri) {
            form.append(
                'propertyTaxReceipt',
                buildPart(existingFields.propertyTaxRec)
            );
        }

        // Append other fields
        Object.keys(existingFields).forEach((key) => {
            if (
                !['siteImages', 'encumbranceCert', 'propertyTaxRec'].includes(
                    key
                )
            ) {
                form.append(key, existingFields[key]);
            }
        });

        await updateSite(form);
    };

    if (isLoading) {
        return (
            <View
                style={[
                    styles.container,
                    {
                        backgroundColor: theme.BACKGROUND,
                        justifyContent: 'center',
                        alignItems: 'center',
                    },
                ]}
            >
                <ActivityIndicator size="large" color={theme.PRIMARY} />
                <Text
                    style={[
                        styles.subtitle,
                        { color: theme.TEXT_PRIMARY, marginTop: 16 },
                    ]}
                >
                    Loading site details...
                </Text>
            </View>
        );
    }

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
            >
                <BackButton color={theme.WHITE} />

                {/* Background Image */}
                <View style={styles.backgroundContainer}>
                    <Image
                        source={require('../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>

                <View style={styles.contentContainer}>
                    <View
                        style={[
                            styles.formContainer,
                            {
                                shadowColor: theme.SHADOW,
                                backgroundColor: theme.CARD,
                            },
                        ]}
                    >
                        <Text style={[styles.title, { color: theme.PRIMARY }]}>
                            Update Your Site
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Update your property details on Build Connect
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Step{' '}
                            {[
                                'siteDetails',
                                'location',
                                'encumbrance',
                                'propertyTax',
                                'review',
                            ].indexOf(step) + 1}{' '}
                            of 5
                        </Text>

                        {step === 'siteDetails' && (
                            <SiteDetailsStep
                                theme={theme}
                                fields={existingFields}
                                setFields={setFields}
                                setStep={setStep}
                            />
                        )}
                        {step === 'location' && (
                            <LocationStep
                                theme={theme}
                                fields={existingFields}
                                setFields={setFields}
                                region={region}
                                setRegion={setRegion}
                                marker={marker}
                                setMarker={setMarker}
                                hasPermission={hasPermission}
                                setHasPermission={setHasPermission}
                                setStep={setStep}
                            />
                        )}
                        {step === 'encumbrance' && (
                            <EncumbranceStep
                                theme={theme}
                                fields={existingFields}
                                setFields={setFields}
                                setStep={setStep}
                            />
                        )}
                        {step === 'propertyTax' && (
                            <PropertyTaxStep
                                theme={theme}
                                fields={existingFields}
                                setFields={setFields}
                                setStep={setStep}
                            />
                        )}
                        {step === 'review' && (
                            <ReviewStep
                                theme={theme}
                                fields={existingFields}
                                setStep={setStep}
                                handleSubmit={handleSubmit}
                                isSubmitting={isSubmitting}
                            />
                        )}
                    </View>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
};

export default UpdateSite;
