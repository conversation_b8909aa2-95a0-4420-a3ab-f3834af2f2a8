import React from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import { styles } from '../siteStyles';
import { ScrollView } from 'react-native-gesture-handler';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB
const MAX_SITE_IMAGES = 10;

const SiteDetailsStep = ({ theme, fields, setFields, setStep }) => {
    const handleNext = () => {
        if (!fields.name || !fields.addressLine1 || !fields.pincode) {
            return;
        }
        setStep('location');
    };

    const pickSiteImages = async () => {
        try {
            const res = await DocumentPicker.getDocumentAsync({
                type: ALLOWED_TYPES,
                multiple: true,
            });
            if (res.canceled) return;

            const validAssets = [];
            for (const asset of res.assets) {
                if (asset.size > MAX_FILE_SIZE) {
                    Alert.alert(
                        'File too large',
                        `${asset.name} is too large. Max 5 MB allowed.`
                    );
                    continue;
                }
                if (!ALLOWED_TYPES.includes(asset.mimeType)) {
                    Alert.alert(
                        'Invalid type',
                        `${asset.name} is not a valid file type. Choose JPG, PNG, or PDF`
                    );
                    continue;
                }
                validAssets.push(asset);
            }

            if (
                fields.siteImages.length + validAssets.length >
                MAX_SITE_IMAGES
            ) {
                Alert.alert(
                    'Limit reached',
                    `Maximum ${MAX_SITE_IMAGES} files allowed.`
                );
                return;
            }

            setFields((prev) => ({
                ...prev,
                siteImages: [...prev.siteImages, ...validAssets],
            }));
        } catch (error) {
            Alert.alert('Error', 'Failed to select files');
        }
    };

    const removeImage = (index) => {
        setFields((prev) => ({
            ...prev,
            siteImages: prev.siteImages.filter((_, i) => i !== index),
        }));
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Basic Details
            </Text>

            {/* Site Name */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="business-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Site name"
                    value={fields.name}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, name: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Address Line 1 */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Address line 1"
                    value={fields.addressLine1}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, addressLine1: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Address Line 2 */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Address line 2"
                    value={fields.addressLine2}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, addressLine2: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Landmark */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="flag-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Landmark"
                    value={fields.landmark}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, landmark: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Pincode */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="mail-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Pincode"
                    value={fields.pincode}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, pincode: text }))
                    }
                    keyboardType="numeric"
                    maxLength={6}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* State */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="map-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="State"
                    value={fields.state}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, state: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* District */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="map-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="District"
                    value={fields.district}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, district: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Plot Area */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="resize-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Plot area (sq ft)"
                    value={fields.plotArea}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, plotArea: text }))
                    }
                    keyboardType="numeric"
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Price */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="wallet-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Price (₹)"
                    value={fields.price}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, price: text }))
                    }
                    keyboardType="numeric"
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Site Images */}
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Site Images
            </Text>
            <TouchableOpacity
                style={[
                    styles.filePickerButton,
                    {
                        backgroundColor:
                            fields.siteImages.length > 0
                                ? theme.ACCENT
                                : theme.INPUT_BACKGROUND,
                        borderColor:
                            fields.siteImages.length > 0
                                ? theme.PRIMARY
                                : theme.INPUT_BORDER,
                    },
                ]}
                onPress={pickSiteImages}
            >
                <Ionicons
                    name={
                        fields.siteImages.length > 0
                            ? 'checkmark-circle'
                            : 'cloud-upload-outline'
                    }
                    size={22}
                    color={
                        fields.siteImages.length > 0
                            ? theme.PRIMARY
                            : theme.TEXT_SECONDARY
                    }
                    style={styles.inputIcon}
                />
                <Text
                    style={[
                        styles.filePickerText,
                        {
                            color:
                                fields.siteImages.length > 0
                                    ? theme.PRIMARY
                                    : theme.TEXT_SECONDARY,
                        },
                    ]}
                >
                    {fields.siteImages.length > 0
                        ? `${fields.siteImages.length} image${fields.siteImages.length > 1 ? 's' : ''} selected`
                        : `Pick site images (JPG/PNG/PDF, max ${MAX_SITE_IMAGES})`}
                </Text>
            </TouchableOpacity>

            {/* Display selected images */}
            {fields.siteImages.length > 0 && (
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.imageGrid}
                >
                    {fields.siteImages.map((image, index) => (
                        <View
                            key={index}
                            style={[
                                styles.selectedFile,
                                {
                                    backgroundColor: theme.ACCENT,
                                    borderColor: theme.INPUT_BORDER,
                                },
                            ]}
                        >
                            <Ionicons
                                name="document-outline"
                                size={16}
                                color={theme.PRIMARY}
                            />
                            <Text
                                style={[
                                    styles.selectedFileName,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                numberOfLines={1}
                            >
                                {image.name}
                            </Text>
                            <TouchableOpacity>
                                <Ionicons
                                    name="eye-outline"
                                    size={20}
                                    color={theme.GRAY}
                                />
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={styles.removeFileButton}
                                onPress={() => removeImage(index)}
                            >
                                <Ionicons
                                    name="close-circle"
                                    size={20}
                                    color={theme.ERROR}
                                />
                            </TouchableOpacity>
                        </View>
                    ))}
                </ScrollView>
            )}

            {/* Next Button */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[styles.nextButton, { shadowColor: theme.PRIMARY }]}
                    onPress={handleNext}
                    activeOpacity={0.8}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.nextButtonGradient}
                    >
                        <Text
                            style={[
                                styles.nextButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default SiteDetailsStep;
