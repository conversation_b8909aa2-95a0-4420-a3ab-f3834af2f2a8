import React from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { styles } from '../siteStyles';
import FilePicker from '../FilePicker';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB
const MAX_SITE_IMAGES = 10;

const SiteDetailsStep = ({ theme, fields, setFields, setStep }) => {
    const handleNext = () => {
        if (!fields.name || !fields.addressLine1 || !fields.pincode) {
            return;
        }
        setStep('location');
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Basic Details
            </Text>

            {/* Site Name */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="business-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Site name*"
                    value={fields.name}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, name: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Address Line 1 */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Address line 1*"
                    value={fields.addressLine1}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, addressLine1: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Address Line 2 */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Address line 2"
                    value={fields.addressLine2}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, addressLine2: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Landmark */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="flag-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Landmark"
                    value={fields.landmark}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, landmark: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Pincode */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="mail-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Pincode*"
                    value={fields.pincode}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, pincode: text }))
                    }
                    keyboardType="numeric"
                    maxLength={6}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* State */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="map-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="State"
                    value={fields.state}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, state: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* District */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="map-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="District"
                    value={fields.district}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, district: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Plot Area */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="resize-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Plot area (sq ft)*"
                    value={fields.plotArea}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, plotArea: text }))
                    }
                    keyboardType="numeric"
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Price */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="cash-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Price (₹)"
                    value={fields.price}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, price: text }))
                    }
                    keyboardType="numeric"
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Site Images */}
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Site Images
            </Text>
            <FilePicker
                label={`Pick site images (JPG/PNG/PDF, max ${MAX_SITE_IMAGES})`}
                files={fields.siteImages}
                setFiles={setFields}
                keyName="siteImages"
                maxFiles={MAX_SITE_IMAGES}
                allowedTypes={ALLOWED_TYPES}
                maxFileSize={MAX_FILE_SIZE}
                isMultiple={true}
                theme={theme}
            />

            {/* Next Button */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[
                        styles.nextButton,
                        { shadowColor: theme.PRIMARY },
                    ]}
                    onPress={handleNext}
                    activeOpacity={0.8}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.nextButtonGradient}
                    >
                        <Text
                            style={[
                                styles.nextButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default SiteDetailsStep;
