import React, { useEffect, useRef, useContext } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Animated,
    ScrollView,
    Image,
    Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import BackButton from '../Components/Shared/BackButton';
import { ThemeContext } from '../../context/ThemeContext';

const { height } = Dimensions.get('window');

const SiteSuccess = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const scaleAnim = useRef(new Animated.Value(0)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        // Start animations when component mounts
        Animated.parallel([
            Animated.spring(scaleAnim, {
                toValue: 1,
                tension: 50,
                friction: 7,
                useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 800,
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    return (
        <View style={{ flex: 1, backgroundColor: theme.BACKGROUND }}>
            {/* Background Image & Gradient */}
            <View style={styles.backgroundContainer}>
                <Image
                    source={require('../../assets/images/background.png')}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={[theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY]}
                    style={styles.backgroundOverlay}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                />
            </View>
            <ScrollView
                contentContainerStyle={{ flexGrow: 1, minHeight: height }}
            >
                <BackButton
                    color={theme.WHITE}
                    onPress={() => router.push('/Home')}
                />
                <View style={styles.contentContainer}>
                    <View
                        style={[
                            styles.card,
                            {
                                backgroundColor: theme.CARD,
                                shadowColor: theme.SHADOW,
                            },
                        ]}
                    >
                        <Animated.View
                            style={[
                                styles.iconWrapper,
                                { shadowColor: theme.PRIMARY },
                                {
                                    transform: [{ scale: scaleAnim }],
                                    opacity: fadeAnim,
                                },
                            ]}
                        >
                            <Ionicons
                                name="checkmark-circle"
                                size={90}
                                color={theme.PRIMARY}
                            />
                        </Animated.View>
                        <Animated.Text
                            style={[
                                styles.title,
                                { color: theme.PRIMARY, opacity: fadeAnim },
                            ]}
                        >
                            Site Listed Successfully!
                        </Animated.Text>
                        <Animated.Text
                            style={[
                                styles.subtitle,
                                {
                                    color: theme.TEXT_PRIMARY,
                                    opacity: fadeAnim,
                                },
                            ]}
                        >
                            Your site listing is pending verification.
                        </Animated.Text>
                        <Animated.Text
                            style={[
                                styles.nextSteps,
                                {
                                    color: theme.TEXT_SECONDARY,
                                    opacity: fadeAnim,
                                },
                            ]}
                        >
                            What happens next:
                        </Animated.Text>
                        <Animated.View
                            style={[
                                styles.stepsList,
                                { opacity: fadeAnim },
                            ]}
                        >
                            <View style={styles.stepItem}>
                                <Ionicons
                                    name="document-text-outline"
                                    size={20}
                                    color={theme.PRIMARY}
                                />
                                <Text
                                    style={[
                                        styles.stepText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Our team will verify your documents
                                </Text>
                            </View>
                            <View style={styles.stepItem}>
                                <Ionicons
                                    name="time-outline"
                                    size={20}
                                    color={theme.PRIMARY}
                                />
                                <Text
                                    style={[
                                        styles.stepText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Verification typically takes 3-5 business days
                                </Text>
                            </View>
                            <View style={styles.stepItem}>
                                <Ionicons
                                    name="notifications-outline"
                                    size={20}
                                    color={theme.PRIMARY}
                                />
                                <Text
                                    style={[
                                        styles.stepText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    You'll receive a notification once approved
                                </Text>
                            </View>
                            <View style={styles.stepItem}>
                                <Ionicons
                                    name="globe-outline"
                                    size={20}
                                    color={theme.PRIMARY}
                                />
                                <Text
                                    style={[
                                        styles.stepText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Your site will be visible to potential buyers
                                </Text>
                            </View>
                        </Animated.View>
                        <Animated.View
                            style={[
                                styles.buttonContainer,
                                { opacity: fadeAnim },
                            ]}
                        >
                            <TouchableOpacity
                                style={[
                                    styles.button,
                                    { shadowColor: theme.PRIMARY },
                                ]}
                                onPress={() => router.push('/Home')}
                                activeOpacity={0.8}
                            >
                                <LinearGradient
                                    colors={[theme.PRIMARY, theme.SECONDARY]}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 0 }}
                                    style={styles.buttonGradient}
                                >
                                    <Text
                                        style={[
                                            styles.buttonText,
                                            { color: theme.WHITE },
                                        ]}
                                    >
                                        Back to Home
                                    </Text>
                                </LinearGradient>
                            </TouchableOpacity>
                        </Animated.View>
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    contentContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingTop: 60,
    },
    card: {
        width: '100%',
        maxWidth: 400,
        borderRadius: 24,
        padding: 32,
        alignItems: 'center',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.15,
        shadowRadius: 16,
        elevation: 10,
    },
    iconWrapper: {
        marginBottom: 24,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 5,
    },
    title: {
        fontSize: 28,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 12,
    },
    subtitle: {
        fontSize: 18,
        textAlign: 'center',
        marginBottom: 24,
        lineHeight: 26,
    },
    nextSteps: {
        fontSize: 16,
        fontWeight: '600',
        textAlign: 'center',
        marginBottom: 20,
    },
    stepsList: {
        width: '100%',
        marginBottom: 32,
    },
    stepItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
        paddingHorizontal: 8,
    },
    stepText: {
        fontSize: 15,
        marginLeft: 12,
        flex: 1,
        lineHeight: 22,
    },
    buttonContainer: {
        width: '100%',
    },
    button: {
        borderRadius: 16,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 5,
    },
    buttonGradient: {
        borderRadius: 16,
        paddingVertical: 16,
        paddingHorizontal: 32,
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },
});

export default SiteSuccess;
