import React from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import { styles } from '../siteStyles';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB

const PropertyTaxStep = ({ theme, fields, setFields, setStep }) => {
    const handleNext = () => {
        setStep('review');
    };

    const handleBack = () => {
        setStep('encumbrance');
    };

    const pickPropertyTaxDocument = async () => {
        try {
            const res = await DocumentPicker.getDocumentAsync({
                type: ALLOWED_TYPES,
            });
            if (res.canceled) return;

            const asset = res.assets[0];
            if (asset.size > MAX_FILE_SIZE) {
                Alert.alert('File too large', 'Max 5 MB allowed.');
                return;
            }
            if (!ALLOWED_TYPES.includes(asset.mimeType)) {
                Alert.alert('Invalid type', 'Choose JPG, PNG, or PDF');
                return;
            }

            setFields((prev) => ({
                ...prev,
                propertyTaxRec: asset,
            }));
        } catch (error) {
            Alert.alert('Error', 'Failed to select file');
        }
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Property Tax Receipt Details
            </Text>

            {/* Owner Name */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="person-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Owner name*"
                    value={fields.ptrOwnerName}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, ptrOwnerName: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Receipt Number */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="receipt-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Receipt number*"
                    value={fields.ptrReciptNo}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, ptrReciptNo: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Property Tax Receipt Upload */}
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Upload Property Tax Receipt
            </Text>
            <TouchableOpacity
                style={[
                    styles.filePickerButton,
                    {
                        backgroundColor: fields.propertyTaxRec
                            ? theme.ACCENT
                            : theme.INPUT_BACKGROUND,
                        borderColor: fields.propertyTaxRec
                            ? theme.PRIMARY
                            : theme.INPUT_BORDER,
                    },
                ]}
                onPress={pickPropertyTaxDocument}
            >
                <Ionicons
                    name={
                        fields.propertyTaxRec
                            ? 'checkmark-circle'
                            : 'receipt-outline'
                    }
                    size={22}
                    color={
                        fields.propertyTaxRec
                            ? theme.PRIMARY
                            : theme.TEXT_SECONDARY
                    }
                    style={styles.inputIcon}
                />
                <Text
                    style={[
                        styles.filePickerText,
                        {
                            color: fields.propertyTaxRec
                                ? theme.PRIMARY
                                : theme.TEXT_SECONDARY,
                        },
                    ]}
                >
                    {fields.propertyTaxRec
                        ? `✓ ${fields.propertyTaxRec.name}`
                        : 'Property Tax Receipt (JPG/PNG/PDF)*'}
                </Text>
            </TouchableOpacity>

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[
                        styles.backButton,
                        { backgroundColor: theme.GRAY_LIGHT },
                    ]}
                    onPress={handleBack}
                    activeOpacity={0.8}
                >
                    <Text
                        style={[
                            styles.backButtonText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Back
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[styles.nextButton, { shadowColor: theme.PRIMARY }]}
                    onPress={handleNext}
                    activeOpacity={0.8}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.nextButtonGradient}
                    >
                        <Text
                            style={[
                                styles.nextButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Review
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default PropertyTaxStep;
