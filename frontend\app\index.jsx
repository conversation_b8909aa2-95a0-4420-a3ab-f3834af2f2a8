import React, { useEffect, useRef, useContext } from 'react';
import {
    StyleSheet,
    View,
    Text,
    Image,
    TouchableOpacity,
    Animated,
    Easing,
    Dimensions,
    StatusBar,
    SafeAreaView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import {
    Ionicons,
    MaterialCommunityIcons,
    FontAwesome5,
} from '@expo/vector-icons';
import { ThemeContext } from '../context/ThemeContext';

const { width, height } = Dimensions.get('window');

export default function Index() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    // Animation refs
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(50)).current;
    const buttonScale = useRef(new Animated.Value(1)).current;

    useEffect(() => {
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 800,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 800,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
        ]).start();
    }, [fadeAnim, slideAnim]);

    const handlePressIn = () => {
        Animated.spring(buttonScale, {
            toValue: 0.95,
            friction: 8,
            useNativeDriver: true,
        }).start();
    };

    const handlePressOut = () => {
        Animated.spring(buttonScale, {
            toValue: 1,
            friction: 8,
            useNativeDriver: true,
        }).start();
    };

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.PRIMARY }]}
        >
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor={theme.PRIMARY}
            />
            {/* Banner Background */}
            <View style={styles.bannerContainer}>
                <Image
                    source={require('../assets/images/background.png')}
                    style={styles.bannerImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={[theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY]}
                    opacity={0.7}
                    style={styles.bannerOverlay}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                />
            </View>

            {/* Logo */}
            <View style={styles.logoContainer}>
                <Image
                    source={require('../assets/images/build-connect.jpg')}
                    style={[styles.logo, { borderColor: theme.LOGO_BORDER }]}
                    resizeMode="cover"
                />
            </View>

            {/* Content */}
            <Animated.View
                style={[
                    styles.contentContainer,
                    {
                        opacity: fadeAnim,
                        transform: [{ translateY: slideAnim }],
                    },
                ]}
            >
                <Text style={[styles.title, { color: theme.WHITE }]}>
                    Welcome to BUILD-CONNECT
                </Text>
                <Text style={[styles.subtitle, { color: theme.WHITE }]}>
                    Your Land & Construction Hub
                </Text>

                {/* Background Gradient */}
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    style={styles.background}
                    start={{ x: 0.5, y: 0.6 }}
                    end={{ x: 1.9, y: 0 }}
                >
                    {/* Features */}
                    <View style={[styles.featuresContainer]}>
                        <View style={styles.featureRow}>
                            <View style={styles.iconContainer}>
                                <Ionicons
                                    name="search"
                                    size={24}
                                    color={theme.WHITE}
                                />
                            </View>
                            <View style={styles.featureTextContainer}>
                                <Text
                                    style={[
                                        styles.featureTitle,
                                        { color: theme.WHITE },
                                    ]}
                                >
                                    Find Properties
                                </Text>
                                <Text style={styles.featureSubtitle}>
                                    Browse verified land listings
                                </Text>
                            </View>
                        </View>

                        <View style={styles.featureRow}>
                            <View style={styles.iconContainer}>
                                <MaterialCommunityIcons
                                    name="account-hard-hat"
                                    size={24}
                                    color={theme.WHITE}
                                />
                            </View>
                            <View style={styles.featureTextContainer}>
                                <Text
                                    style={[
                                        styles.featureTitle,
                                        { color: theme.WHITE },
                                    ]}
                                >
                                    Hire Contractors
                                </Text>
                                <Text style={styles.featureSubtitle}>
                                    Connect with trusted professionals
                                </Text>
                            </View>
                        </View>

                        <View style={styles.featureRow}>
                            <View style={styles.iconContainer}>
                                <FontAwesome5
                                    name="handshake"
                                    size={20}
                                    color={theme.WHITE}
                                />
                            </View>
                            <View style={styles.featureTextContainer}>
                                <Text
                                    style={[
                                        styles.featureTitle,
                                        { color: theme.WHITE },
                                    ]}
                                >
                                    Connect with Site Scouts
                                </Text>
                                <Text style={styles.featureSubtitle}>
                                    For verified land deals
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Buttons */}
                    <View style={styles.buttonContainer}>
                        <TouchableOpacity
                            onPress={() => router.push('/auth/Login')}
                            onPressIn={handlePressIn}
                            onPressOut={handlePressOut}
                            activeOpacity={0.9}
                        >
                            <Animated.View
                                style={[
                                    styles.button,
                                    { shadowColor: theme.SHADOW },
                                    { transform: [{ scale: buttonScale }] },
                                ]}
                            >
                                <LinearGradient
                                    colors={['#ffffff', '#f8f8f8']}
                                    style={styles.buttonGradient}
                                >
                                    <Text
                                        style={[
                                            styles.buttonText,
                                            { color: theme.PRIMARY },
                                        ]}
                                    >
                                        Get Started
                                    </Text>
                                    <Ionicons
                                        name="arrow-forward"
                                        size={22}
                                        color={theme.PRIMARY}
                                        style={styles.buttonIcon}
                                    />
                                </LinearGradient>
                            </Animated.View>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.buttonContainer}>
                        <TouchableOpacity
                            onPress={() =>
                                router.push('/Sites/AddSites/SiteFormNavigator')
                            }
                            onPressIn={handlePressIn}
                            onPressOut={handlePressOut}
                            activeOpacity={0.9}
                        >
                            <Animated.View
                                style={[
                                    styles.button,
                                    { shadowColor: theme.SHADOW },
                                    { transform: [{ scale: buttonScale }] },
                                ]}
                            >
                                <LinearGradient
                                    colors={['#ffffff', '#f8f8f8']}
                                    style={styles.buttonGradient}
                                >
                                    <Text
                                        style={[
                                            styles.buttonText,
                                            { color: theme.PRIMARY },
                                        ]}
                                    >
                                        Get Started
                                    </Text>
                                    <Ionicons
                                        name="arrow-forward"
                                        size={22}
                                        color={theme.PRIMARY}
                                        style={styles.buttonIcon}
                                    />
                                </LinearGradient>
                            </Animated.View>
                        </TouchableOpacity>
                    </View>

                    {/* <View style={styles.buttonContainer}>
                        <TouchableOpacity
                            onPress={() =>
                                router.push('/Sites/AddSites/SiteFormNavigator')
                            }
                            onPressIn={handlePressIn}
                            onPressOut={handlePressOut}
                            activeOpacity={0.9}
                        >
                            <Animated.View
                                style={[
                                    styles.button,
                                    { shadowColor: theme.SHADOW },
                                    { transform: [{ scale: buttonScale }] },
                                ]}
                            >
                                <LinearGradient
                                    colors={['#ffffff', '#f8f8f8']}
                                    style={styles.buttonGradient}
                                >
                                    <Text
                                        style={[
                                            styles.buttonText,
                                            { color: theme.PRIMARY },
                                        ]}
                                    >
                                        Get Started
                                    </Text>
                                    <Ionicons
                                        name="arrow-forward"
                                        size={22}
                                        color={theme.PRIMARY}
                                        style={styles.buttonIcon}
                                    />
                                </LinearGradient>
                            </Animated.View>
                        </TouchableOpacity>
                    </View> */}
                </LinearGradient>
            </Animated.View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    bannerContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '100%',
        zIndex: 1,
    },
    bannerImage: {
        width: '100%',
        height: '100%',
    },
    bannerOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    background: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: height * 0.2,
        bottom: 0,
        zIndex: 2,
        borderTopRightRadius: 20,
        borderTopLeftRadius: 20,
    },
    logoContainer: {
        alignItems: 'center',
        marginTop: height * 0.02,
        zIndex: 3,
    },
    logo: {
        width: width * 0.3,
        height: width * 0.3,
        borderRadius: 20,
        borderWidth: 3,
    },
    contentContainer: {
        flex: 1,
        paddingHorizontal: 24,
        alignItems: 'center',
        zIndex: 3,
    },
    title: {
        fontSize: width * 0.08,
        fontWeight: 'bold',
        textAlign: 'center',
    },
    subtitle: {
        fontSize: width * 0.03,
        fontFamily: 'poppins',
        marginTop: 4,
        textAlign: 'center',
    },
    featuresContainer: {
        width: '100%',
        marginTop: height * 0.03,
        paddingHorizontal: 20,
        zIndex: 2,
    },
    featureRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
    },
    iconContainer: {
        width: 50,
        height: 50,
        borderRadius: 25,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    featureTextContainer: {
        flex: 1,
    },
    featureTitle: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    featureSubtitle: {
        fontSize: 14,
        color: 'rgba(255,255,255,0.8)',
    },
    buttonContainer: {
        width: '100%',
        marginTop: height * 0.06,
        alignItems: 'center',
    },
    button: {
        width: width * 0.8,
        borderRadius: 16,
        overflow: 'hidden',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 5,
        elevation: 5,
    },
    buttonGradient: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 16,
    },
    buttonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    buttonIcon: {
        marginLeft: 8,
    },
    secondaryButton: {
        marginTop: 16,
        paddingVertical: 12,
    },
    secondaryButtonText: {
        fontSize: 16,
        textDecorationLine: 'underline',
    },
});
