import React, { useState, useContext, useRef } from 'react';
import {
    View,
    Text,
    ScrollView,
    KeyboardAvoidingView,
    Platform,
    Image,
    Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';
import { showToast } from '../../utils/showToast';
import { styles } from './siteStyles';

// Import step components (to be created)
import SiteDetailsStep from './AddSites/SiteDetailsStep';
import LocationStep from './AddSites/LocationStep';
import EncumbranceStep from './AddSites/EncumbranceStep';
import PropertyTaxStep from './AddSites/PropertyTaxStep';
import ReviewStep from './AddSites/ReviewStep';

const { height } = Dimensions.get('window');

const AddSites = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const queryClient = useQueryClient();
    const [step, setStep] = useState('siteDetails');
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Form state
    const [fields, setFields] = useState({
        name: '',
        addressLine1: '',
        addressLine2: '',
        landmark: '',
        location: '',
        pincode: '',
        state: '',
        district: '',
        plotArea: '',
        price: '',
        latitude: '',
        longitude: '',
        encOwnerName: '',
        encDocumentNo: '',
        surveyNo: '',
        village: '',
        subDistrict: '',
        District: '',
        ptrOwnerName: '',
        ptrReciptNo: '',
        siteImages: [],
        encumbranceCert: null,
        propertyTaxRec: null,
    });

    // Map state
    const [region, setRegion] = useState({
        latitude: 37.78825,
        longitude: -122.4324,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
    });

    const [marker, setMarker] = useState(null);
    const [hasPermission, setHasPermission] = useState(false);

    // Site creation mutation
    const {
        mutateAsync: createSite,
        isLoading: isCreating,
        error: createError,
    } = useMutation({
        mutationFn: async (formData) => {
            const { privateAPIClient } = await import('../../api');
            return privateAPIClient.post(
                '/site-service/api/v1/sites',
                formData,
                {
                    headers: { 'Content-Type': 'multipart/form-data' },
                }
            );
        },
        onSuccess: () => {
            setIsSubmitting(false);
            queryClient.invalidateQueries({ queryKey: ['sites'] });
            router.replace('/Sites/SiteSuccess');
        },
        onError: (error) => {
            console.log(error.message);
            setIsSubmitting(false);
            showToast(
                'error',
                'Submission Error',
                'Failed to create site listing.'
            );
        },
    });

    const handleSubmit = async () => {
        if (
            !fields.siteImages?.length ||
            !fields.encumbranceCert ||
            !fields.propertyTaxRec
        ) {
            showToast(
                'error',
                'Missing Documents',
                'Please attach at least one site image and the required documents.'
            );
            return;
        }

        if (!fields.name || !fields.pincode || !fields.plotArea) {
            showToast(
                'error',
                'Missing Fields',
                'Name, pincode, and plot area are required.'
            );
            return;
        }

        setIsSubmitting(true);

        const buildPart = (asset) => ({
            uri: asset.uri,
            name: asset.name || `file_${Date.now()}`,
            type: asset.mimeType || 'application/octet-stream',
        });

        const form = new FormData();
        fields.siteImages.forEach((image) => {
            form.append('siteImages', buildPart(image));
        });
        form.append(
            'encumbranceCertificate',
            buildPart(fields.encumbranceCert)
        );
        form.append('propertyTaxReceipt', buildPart(fields.propertyTaxRec));

        // Append other fields
        Object.keys(fields).forEach((key) => {
            if (
                !['siteImages', 'encumbranceCert', 'propertyTaxRec'].includes(
                    key
                )
            ) {
                form.append(key, fields[key]);
            }
        });

        await createSite(form);
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
            >
                <BackButton color={theme.WHITE} />

                {/* Background Image */}
                <View style={styles.backgroundContainer}>
                    <Image
                        source={require('../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>

                <View style={styles.contentContainer}>
                    <View
                        style={[
                            styles.formContainer,
                            {
                                shadowColor: theme.SHADOW,
                                backgroundColor: theme.CARD,
                            },
                        ]}
                    >
                        <Text style={[styles.title, { color: theme.PRIMARY }]}>
                            List Your Site
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Add your property to Build Connect
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Step{' '}
                            {[
                                'siteDetails',
                                'location',
                                'encumbrance',
                                'propertyTax',
                                'review',
                            ].indexOf(step) + 1}{' '}
                            of 5
                        </Text>

                        {step === 'siteDetails' && (
                            <SiteDetailsStep
                                theme={theme}
                                fields={fields}
                                setFields={setFields}
                                setStep={setStep}
                            />
                        )}
                        {step === 'location' && (
                            <LocationStep
                                theme={theme}
                                fields={fields}
                                setFields={setFields}
                                region={region}
                                setRegion={setRegion}
                                marker={marker}
                                setMarker={setMarker}
                                hasPermission={hasPermission}
                                setHasPermission={setHasPermission}
                                setStep={setStep}
                            />
                        )}
                        {step === 'encumbrance' && (
                            <EncumbranceStep
                                theme={theme}
                                fields={fields}
                                setFields={setFields}
                                setStep={setStep}
                            />
                        )}
                        {step === 'propertyTax' && (
                            <PropertyTaxStep
                                theme={theme}
                                fields={fields}
                                setFields={setFields}
                                setStep={setStep}
                            />
                        )}
                        {step === 'review' && (
                            <ReviewStep
                                theme={theme}
                                fields={fields}
                                setStep={setStep}
                                handleSubmit={handleSubmit}
                                isSubmitting={isSubmitting}
                            />
                        )}
                    </View>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
};

export default AddSites;
